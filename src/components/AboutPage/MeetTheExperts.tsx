'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { <PERSON>a<PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, FaGithub, FaEnvelope } from 'react-icons/fa';

interface TeamMember {
  id: string;
  name: string;
  role: string;
  bio: string;
  imageSrc: string;
  order: number;
  linkedinUrl?: string;
  twitterUrl?: string;
  githubUrl?: string;
  emailAddress?: string;
  createdAt?: Date | string;
  updatedAt?: Date | string;
}

const MeetTheExperts: React.FC = () => {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTeamMembers = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/team');

        if (!response.ok) {
          throw new Error('Failed to fetch team members');
        }

        const data = await response.json();
        setTeamMembers(data);
      } catch (err) {
        console.error('Error fetching team members:', err);
        setError('Failed to load team members');
        // Use fallback data in case of error
        setTeamMembers(fallbackTeamMembers);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTeamMembers();
  }, []);

  // Fallback data with bio information
  const fallbackTeamMembers: TeamMember[] = [
    {
      id: '1',
      name: 'Jack Sequeira Onyango',
      role: 'Graphics Designer',
      bio: 'With a sharp eye for detail and a deep understanding of design trends and tools like Adobe Creative Suite (Photoshop, Illustrator, InDesign), I help brands tell their stories visually and build lasting connections with their audiences.',
      imageSrc: '/images/team/jack-sequeira.jpg',
      order: 0,
      linkedinUrl: 'https://linkedin.com/in/jacksequeira',
    },
    {
      id: '2',
      name: 'Collins Kimokoti',
      role: 'Video Editor',
      bio: 'Collins Kimokoti is a Senior Graphics Designer and Video Editor with a passion for creating striking visuals that bring ideas to life. With extensive experience in branding, digital marketing design, and video production.',
      imageSrc: '/images/team/collins-kimokoti.jpg',
      order: 1,
      linkedinUrl: 'https://linkedin.com/in/collinskimokoti',
    },
    {
      id: '3',
      name: 'Anita Kay',
      role: 'Executive Assistant | Social Media Expert',
      bio: 'Anita Kay is a dynamic marketing strategist with a keen eye for consumer behavior and digital trends. She specializes in building brand awareness, driving engagement, and creating compelling marketing campaigns.',
      imageSrc: '/images/team/anita-kay.jpg',
      order: 2,
      linkedinUrl: 'https://linkedin.com/in/anitakay',
    }
  ];

  // Use API data if available, otherwise use fallback
  const displayTeamMembers = teamMembers.length > 0 ? teamMembers : fallbackTeamMembers;

  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Meet the Experts</h2>
          <div className="w-24 h-1 bg-orange-500 mx-auto mb-6"></div>
          <p className="text-lg text-gray-700 max-w-2xl mx-auto">
            Our talented team of professionals is dedicated to bringing your digital vision to life.
          </p>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"></div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {displayTeamMembers.map((member) => (
              <div
                key={member.id}
                className="bg-white rounded-xl overflow-hidden shadow-lg transition-all duration-300 hover:shadow-2xl hover:transform hover:scale-105 group"
              >
                <div className="relative h-80 w-full overflow-hidden">
                  <Image
                    src={member.imageSrc}
                    alt={member.name}
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    className="object-cover transition-transform duration-300 group-hover:scale-110"
                    priority
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>

                <div className="p-6">
                  <div className="text-center mb-4">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{member.name}</h3>
                    <p className="text-orange-600 font-semibold mb-3">{member.role}</p>
                  </div>

                  <div className="mb-4">
                    <p className="text-gray-600 text-sm leading-relaxed line-clamp-4">
                      {member.bio}
                    </p>
                  </div>

                  <div className="flex justify-center space-x-4 pt-4 border-t border-gray-100">
                    {member.linkedinUrl && (
                      <Link
                        href={member.linkedinUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 transition-colors duration-200 p-2 rounded-full hover:bg-blue-50"
                      >
                        <FaLinkedin size={20} />
                        <span className="sr-only">LinkedIn</span>
                      </Link>
                    )}
                    {member.twitterUrl && (
                      <Link
                        href={member.twitterUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-400 hover:text-blue-600 transition-colors duration-200 p-2 rounded-full hover:bg-blue-50"
                      >
                        <FaTwitter size={20} />
                        <span className="sr-only">Twitter</span>
                      </Link>
                    )}
                    {member.githubUrl && (
                      <Link
                        href={member.githubUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-gray-800 hover:text-gray-600 transition-colors duration-200 p-2 rounded-full hover:bg-gray-50"
                      >
                        <FaGithub size={20} />
                        <span className="sr-only">GitHub</span>
                      </Link>
                    )}
                    {member.emailAddress && (
                      <Link
                        href={`mailto:${member.emailAddress}`}
                        className="text-red-500 hover:text-red-700 transition-colors duration-200 p-2 rounded-full hover:bg-red-50"
                      >
                        <FaEnvelope size={20} />
                        <span className="sr-only">Email</span>
                      </Link>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </section>
  );
};

export default MeetTheExperts;
