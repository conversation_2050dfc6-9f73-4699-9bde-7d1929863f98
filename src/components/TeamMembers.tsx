'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { TeamMember } from '@/types/team';

export default function TeamMembers() {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    // Function to fetch team members from the API
    const fetchTeamMembers = async () => {
      try {
        setLoading(true);

        // Fetch team members from the API
        const response = await fetch('/api/team');
        const data = await response.json();

        // Check if the response is successful
        if (!response.ok) {
          console.error('API error:', data);
          throw new Error(data.error || `Error: ${response.status}`);
        }

        // Check if data is an array
        if (Array.isArray(data)) {
          console.log('Team members loaded:', data.length);
          setTeamMembers(data);
        } else if (data.error) {
          throw new Error(data.error);
        } else {
          // If no team members are found, use mock data
          console.log('No team members found, using mock data');
          setTeamMembers(getFallbackTeamMembers());
        }
      } catch (err) {
        console.error('Failed to load team members:', err);
        setError('Failed to load team members');
        // Use mock data as fallback
        setTeamMembers(getFallbackTeamMembers());
      } finally {
        setLoading(false);
      }
    };

    fetchTeamMembers();
  }, []);

  // Fallback team members data that matches the screenshot
  const getFallbackTeamMembers = (): TeamMember[] => {
    return [
      {
        id: '1',
        name: 'Don Omondi Onyango',
        role: 'Graphics Designer | Web Developer',
        bio: 'Creative designer with expertise in web development and graphic design.',
        imageSrc: '/images/team/don-omondi.jpg',
        order: 0,
        linkedinUrl: 'https://linkedin.com/in/donomondi',
        githubUrl: 'https://github.com/onyangodonomondi',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '2',
        name: 'Jack Sequeira Onyango',
        role: 'Graphics Designer',
        bio: 'Passionate graphic designer with an eye for detail and creative solutions.',
        imageSrc: '/images/team/jack-sequeira.jpg',
        order: 1,
        linkedinUrl: 'https://linkedin.com/in/jacksequeira',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '3',
        name: 'Sarah Wanjiku',
        role: 'UI/UX Designer',
        bio: 'Experienced UI/UX designer focused on creating intuitive and engaging user experiences.',
        imageSrc: '/images/team/sarah-wanjiku.jpg',
        order: 2,
        linkedinUrl: 'https://linkedin.com/in/sarahwanjiku',
        twitterUrl: 'https://twitter.com/sarahwanjiku',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '4',
        name: 'James Mwangi',
        role: 'Frontend Developer',
        bio: 'Frontend developer specializing in React and modern JavaScript frameworks.',
        imageSrc: '/images/team/james-mwangi.jpg',
        order: 3,
        githubUrl: 'https://github.com/jamesmwangi',
        linkedinUrl: 'https://linkedin.com/in/jamesmwangi',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];
  };

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF5400]"></div>
      </div>
    );
  }

  if (error) {
    return null; // Don't show error on the frontend
  }

  if (teamMembers.length === 0) {
    return null; // Don't show empty section
  }

  return (
    <section className="py-16 relative overflow-hidden bg-white">
      <div className="container mx-auto px-4 relative z-10">
        {/* Section header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
            Meet the Experts
          </h2>
          <div className="h-1 w-24 bg-[#FF5400] mx-auto mb-6"></div>
          <p className="text-gray-700 text-lg max-w-2xl mx-auto">
            Our talented team of professionals is dedicated to bringing your digital vision to life.
          </p>
        </motion.div>

        {/* Team members grid - 3 experts per row */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 max-w-6xl mx-auto">
          {teamMembers.slice(0, 6).map((member, index) => (
            <motion.div
              key={member.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="flex flex-col items-center"
            >
              {/* Image container - Square aspect ratio */}
              <div className="relative w-full aspect-square mb-4 overflow-hidden">
                <Image
                  src={member.imageSrc}
                  alt={member.name}
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 33vw, 25vw"
                  className="object-cover"
                  priority
                />
              </div>

              {/* Name and role - Centered text */}
              <div className="text-center">
                <h3 className="text-xl font-bold text-gray-900 mb-1">{member.name}</h3>
                <p className="text-[#FF5400] text-sm">{member.role}</p>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
