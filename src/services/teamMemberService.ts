import prisma from '@/lib/prisma';

export interface TeamMember {
  id: string;
  name: string;
  role: string;
  bio: string;
  imageSrc: string;
  order: number;
  linkedinUrl?: string | null;
  twitterUrl?: string | null;
  githubUrl?: string | null;
  emailAddress?: string | null;
  createdAt: string;
  updatedAt: string;
}

// Get all team members
export async function getAllTeamMembers(): Promise<TeamMember[]> {
  try {
    console.log('Fetching all team members from database');
    const teamMembers = await prisma.teamMember.findMany({
      orderBy: {
        order: 'asc'
      }
    });

    console.log(`Found ${teamMembers.length} team members in database`);
    return teamMembers.map(formatTeamMember);
  } catch (error) {
    console.error('Error getting all team members:', error);
    return [];
  }
}

// Get a team member by ID
export async function getTeamMemberById(id: string): Promise<TeamMember | null> {
  try {
    console.log(`Fetching team member with ID: ${id}`);
    const teamMember = await prisma.teamMember.findUnique({
      where: { id }
    });

    if (!teamMember) {
      console.log(`No team member found with ID: ${id}`);
      return null;
    }

    return formatTeamMember(teamMember);
  } catch (error) {
    console.error(`Error getting team member with ID ${id}:`, error);
    return null;
  }
}

// Create a new team member
export async function createTeamMember(data: Omit<TeamMember, 'id' | 'createdAt' | 'updatedAt'>): Promise<TeamMember> {
  try {
    console.log('Creating new team member:', data.name);
    const newTeamMember = await prisma.teamMember.create({
      data: {
        name: data.name,
        role: data.role,
        bio: data.bio,
        imageSrc: data.imageSrc,
        order: data.order || 0,
        linkedinUrl: data.linkedinUrl || null,
        twitterUrl: data.twitterUrl || null,
        githubUrl: data.githubUrl || null,
        emailAddress: data.emailAddress || null
      }
    });

    console.log(`Created new team member with ID: ${newTeamMember.id}`);
    return formatTeamMember(newTeamMember);
  } catch (error) {
    console.error('Error creating team member:', error);
    throw error;
  }
}

// Update a team member
export async function updateTeamMember(id: string, data: Partial<Omit<TeamMember, 'id' | 'createdAt' | 'updatedAt'>>): Promise<TeamMember> {
  try {
    console.log(`Updating team member with ID: ${id}`);
    const updatedTeamMember = await prisma.teamMember.update({
      where: { id },
      data
    });

    console.log(`Updated team member with ID: ${id}`);
    return formatTeamMember(updatedTeamMember);
  } catch (error) {
    console.error(`Error updating team member with ID ${id}:`, error);
    throw error;
  }
}

// Delete a team member
export async function deleteTeamMember(id: string): Promise<boolean> {
  try {
    console.log(`Deleting team member with ID: ${id}`);
    await prisma.teamMember.delete({
      where: { id }
    });

    console.log(`Deleted team member with ID: ${id}`);
    return true;
  } catch (error) {
    console.error(`Error deleting team member with ID ${id}:`, error);
    return false;
  }
}

// Helper function to format team member data from the database
function formatTeamMember(teamMember: any): TeamMember {
  return {
    id: teamMember.id,
    name: teamMember.name,
    role: teamMember.role,
    bio: teamMember.bio,
    imageSrc: teamMember.imageSrc,
    order: teamMember.order,
    linkedinUrl: teamMember.linkedinUrl,
    twitterUrl: teamMember.twitterUrl,
    githubUrl: teamMember.githubUrl,
    emailAddress: teamMember.emailAddress,
    createdAt: teamMember.createdAt.toISOString(),
    updatedAt: teamMember.updatedAt.toISOString()
  };
}
