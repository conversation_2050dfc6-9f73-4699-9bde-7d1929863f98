'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { 
  PlusIcon, 
  ArrowPathIcon, 
  PencilIcon, 
  TrashIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from '@heroicons/react/24/outline';
import { TeamMember } from '@/types/team';
import { toast } from 'react-hot-toast';

export default function TeamMembersPage() {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const fetchTeamMembers = async () => {
    try {
      setLoading(true);
      setError('');
      
      const response = await fetch('/api/admin/team');
      
      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }
      
      const data = await response.json();
      setTeamMembers(data);
    } catch (err) {
      setError('Failed to load team members');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTeamMembers();
  }, []);

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this team member?')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/team/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      toast.success('Team member deleted successfully');
      fetchTeamMembers();
    } catch (err) {
      toast.error('Failed to delete team member');
      console.error(err);
    }
  };

  const handleReorder = async (id: string, direction: 'up' | 'down') => {
    const currentIndex = teamMembers.findIndex(member => member.id === id);
    if (
      (direction === 'up' && currentIndex === 0) || 
      (direction === 'down' && currentIndex === teamMembers.length - 1)
    ) {
      return;
    }

    const newOrder = [...teamMembers];
    const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    
    // Swap the order values
    const currentOrder = newOrder[currentIndex].order;
    newOrder[currentIndex].order = newOrder[targetIndex].order;
    newOrder[targetIndex].order = currentOrder;
    
    // Update both team members
    try {
      const formData1 = new FormData();
      formData1.append('name', newOrder[currentIndex].name);
      formData1.append('role', newOrder[currentIndex].role);
      formData1.append('bio', newOrder[currentIndex].bio);
      formData1.append('order', newOrder[currentIndex].order.toString());
      
      if (newOrder[currentIndex].linkedinUrl) {
        formData1.append('linkedinUrl', newOrder[currentIndex].linkedinUrl);
      }
      if (newOrder[currentIndex].twitterUrl) {
        formData1.append('twitterUrl', newOrder[currentIndex].twitterUrl);
      }
      if (newOrder[currentIndex].githubUrl) {
        formData1.append('githubUrl', newOrder[currentIndex].githubUrl);
      }
      if (newOrder[currentIndex].emailAddress) {
        formData1.append('emailAddress', newOrder[currentIndex].emailAddress);
      }

      const formData2 = new FormData();
      formData2.append('name', newOrder[targetIndex].name);
      formData2.append('role', newOrder[targetIndex].role);
      formData2.append('bio', newOrder[targetIndex].bio);
      formData2.append('order', newOrder[targetIndex].order.toString());
      
      if (newOrder[targetIndex].linkedinUrl) {
        formData2.append('linkedinUrl', newOrder[targetIndex].linkedinUrl);
      }
      if (newOrder[targetIndex].twitterUrl) {
        formData2.append('twitterUrl', newOrder[targetIndex].twitterUrl);
      }
      if (newOrder[targetIndex].githubUrl) {
        formData2.append('githubUrl', newOrder[targetIndex].githubUrl);
      }
      if (newOrder[targetIndex].emailAddress) {
        formData2.append('emailAddress', newOrder[targetIndex].emailAddress);
      }

      await Promise.all([
        fetch(`/api/admin/team/${newOrder[currentIndex].id}`, {
          method: 'PUT',
          body: formData1,
        }),
        fetch(`/api/admin/team/${newOrder[targetIndex].id}`, {
          method: 'PUT',
          body: formData2,
        })
      ]);

      toast.success('Team member order updated');
      fetchTeamMembers();
    } catch (err) {
      toast.error('Failed to update team member order');
      console.error(err);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-slate-800">Team Members</h1>
        <div className="flex space-x-2">
          <button
            onClick={fetchTeamMembers}
            className="p-2 rounded-lg text-slate-500 hover:text-slate-700 hover:bg-slate-100"
            disabled={loading}
          >
            <ArrowPathIcon className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
          </button>
          <Link
            href="/admin/team/new"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600"
          >
            <PlusIcon className="h-4 w-4 mr-1" />
            New Team Member
          </Link>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center py-8">
          <ArrowPathIcon className="h-8 w-8 animate-spin text-orange-500" />
        </div>
      ) : error ? (
        <div className="bg-red-50 p-4 rounded-md">
          <p className="text-red-800">{error}</p>
        </div>
      ) : teamMembers.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-lg shadow-sm">
          <svg
            className="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            aria-hidden="true"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
          </svg>
          <h3 className="mt-2 text-lg font-medium text-slate-800">No team members found</h3>
          <p className="mt-1 text-slate-500">Get started by adding your first team member.</p>
          <div className="mt-6">
            <Link
              href="/admin/team/new"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600"
            >
              <PlusIcon className="h-4 w-4 mr-1" />
              Add your first team member
            </Link>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {teamMembers.map((member) => (
            <div key={member.id} className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="p-6">
                <div className="flex items-start">
                  <div className="flex-shrink-0 h-16 w-16 relative rounded-full overflow-hidden bg-gray-100">
                    <Image
                      src={member.imageSrc}
                      alt={member.name}
                      width={64}
                      height={64}
                      className="object-cover"
                    />
                  </div>
                  <div className="ml-4 flex-1">
                    <h3 className="text-lg font-medium text-gray-900">{member.name}</h3>
                    <p className="text-sm text-gray-500">{member.role}</p>
                    <p className="mt-1 text-sm text-gray-600 line-clamp-2">{member.bio}</p>
                  </div>
                </div>
                <div className="mt-4 flex justify-between items-center">
                  <div className="text-sm text-gray-500">
                    Added {formatDate(member.createdAt)}
                  </div>
                  <div className="flex space-x-2">
                    <button 
                      onClick={() => handleReorder(member.id, 'up')}
                      className="text-gray-400 hover:text-gray-600"
                      disabled={teamMembers.indexOf(member) === 0}
                    >
                      <ArrowUpIcon className="h-5 w-5" />
                    </button>
                    <button 
                      onClick={() => handleReorder(member.id, 'down')}
                      className="text-gray-400 hover:text-gray-600"
                      disabled={teamMembers.indexOf(member) === teamMembers.length - 1}
                    >
                      <ArrowDownIcon className="h-5 w-5" />
                    </button>
                    <Link href={`/admin/team/${member.id}/edit`} className="text-blue-400 hover:text-blue-600">
                      <PencilIcon className="h-5 w-5" />
                    </Link>
                    <button 
                      onClick={() => handleDelete(member.id)}
                      className="text-red-400 hover:text-red-600"
                    >
                      <TrashIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
