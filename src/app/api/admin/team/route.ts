import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { v4 as uuidv4 } from 'uuid';
import prisma from '@/lib/prisma';
import { TeamMember } from '@/types/team';
import { uploadImageToS3 } from '@/utils/s3Utils';

// GET handler - Get all team members
export async function GET() {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    // Fetch team members from database
    const teamMembers = await prisma.teamMember.findMany({
      orderBy: { order: 'asc' }
    });

    // Return the team members with no-cache headers
    return NextResponse.json(teamMembers, {
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Content-Type': 'application/json'
      },
    });
  } catch (error) {
    console.error('Error fetching team members:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
}

// POST handler - Create a new team member
export async function POST(request: NextRequest) {
  console.log('POST /api/admin/team - Starting team member creation');
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      console.log('POST /api/admin/team - Authentication failed');
      return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
    console.log('POST /api/admin/team - Authentication successful');

    // Parse form data
    const formData = await request.formData();
    console.log('POST /api/admin/team - Received form data');

    const name = formData.get('name') as string;
    const role = formData.get('role') as string;
    const bio = formData.get('bio') as string;
    const order = parseInt(formData.get('order') as string) || 0;
    const linkedinUrl = formData.get('linkedinUrl') as string || null;
    const twitterUrl = formData.get('twitterUrl') as string || null;
    const githubUrl = formData.get('githubUrl') as string || null;
    const emailAddress = formData.get('emailAddress') as string || null;
    const image = formData.get('image') as File;

    console.log('POST /api/admin/team - Form data parsed:', {
      name,
      role,
      bioLength: bio ? bio.length : 0,
      order,
      hasImage: !!image,
      imageType: image ? image.type : 'none',
      imageSize: image ? image.size : 0,
      imageName: image ? image.name : 'none'
    });

    // Validate required fields
    if (!name || !role || !bio || !image) {
      console.log('POST /api/admin/team - Validation failed: Missing required fields', {
        hasName: !!name,
        hasRole: !!role,
        hasBio: !!bio,
        hasImage: !!image
      });
      return new NextResponse(JSON.stringify({ error: 'Missing required fields' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
    console.log('POST /api/admin/team - Validation successful');

    // Generate a unique ID for the team member
    const id = uuidv4();

    // Validate image before uploading
    if (image.size > 15 * 1024 * 1024) {
      return new NextResponse(JSON.stringify({
        error: 'Image file size exceeds 15MB limit. Please upload a smaller image.'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    // Warn about large images that might cause timeouts
    if (image.size > 5 * 1024 * 1024) {
      console.warn(`Large image detected (${Math.round(image.size/1024/1024)}MB). This may cause upload delays.`);
    }

    // Check image type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(image.type)) {
      return new NextResponse(JSON.stringify({
        error: 'Invalid image format. Allowed formats are: JPEG, PNG, GIF, and WebP.'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    // Upload image to S3
    let imageSrc = '';
    let uploadWarnings: string[] | undefined;

    try {
      // Create a unique path for the image
      const timestamp = Date.now();

      // Sanitize filename more thoroughly
      const baseName = image.name.split(/[\\/]/).pop() || 'unnamed';

      // Get file extension
      const fileExtension = (baseName.split('.').pop() || '').toLowerCase();
      const validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
      const extension = validExtensions.includes(fileExtension) ? fileExtension : 'jpg';

      // Create a simple alphanumeric name without the extension
      const nameWithoutExtension = baseName.split('.')[0] || 'image';
      const simpleName = nameWithoutExtension
        .replace(/[^a-zA-Z0-9]/g, '_')
        .replace(/_{2,}/g, '_') // Replace multiple underscores with single
        .replace(/^[_]+|[_]+$/g, '') // Trim underscores from start/end
        .substring(0, 30); // Limit name length

      // Use a very simple filename format: timestamp_simplename.extension
      const safeFileName = `${simpleName}.${extension}`;

      // Use a simpler path structure with fewer segments
      const imagePath = `team/${timestamp}_${safeFileName}`;

      console.log(`Uploading team member image: ${imagePath}, size: ${Math.round(image.size/1024)}KB, type: ${image.type}`);

      // Use enhanced upload utility with logging prefix and dynamic timeout based on file size
      const timeoutMs = image.size > 5 * 1024 * 1024
        ? 180000 // 3 minutes for large files
        : image.size > 2 * 1024 * 1024
          ? 120000 // 2 minutes for medium files
          : 60000; // 1 minute for small files

      console.log(`Using timeout of ${timeoutMs/1000} seconds for image upload based on file size of ${Math.round(image.size/1024)}KB`);

      const uploadResult = await uploadImageToS3(image, imagePath, {
        logPrefix: `[NewTeamMember:${id}]`,
        maxRetries: 5, // Increased from 3 to 5
        timeoutMs
      });

      if (!uploadResult.success) {
        console.error('Image upload failed:', uploadResult.error);

        // Use a default image URL as fallback
        imageSrc = '/images/default-profile.jpg';
        console.log(`Using fallback image URL: ${imageSrc}`);

        // Store warnings for response
        uploadWarnings = uploadResult.warnings;
      } else {
        imageSrc = uploadResult.url;
        console.log(`Using uploaded image URL: ${imageSrc}`);

        // If upload was successful but had warnings, store them
        if (uploadResult.warnings && uploadResult.warnings.length > 0) {
          console.warn(`Image upload warnings for new team member ${id}:`, uploadResult.warnings);
          uploadWarnings = uploadResult.warnings;
        }
      }
    } catch (uploadError) {
      console.error('Error during image upload process:', uploadError);
      // Use a default image URL as fallback
      imageSrc = '/images/default-profile.jpg';
      console.log(`Using fallback image URL after error: ${imageSrc}`);
    }

    // Create team member in database
    const teamMember = await prisma.teamMember.create({
      data: {
        id,
        name,
        role,
        bio,
        imageSrc, // This will be either the uploaded image URL or the default image
        order,
        linkedinUrl,
        twitterUrl,
        githubUrl,
        emailAddress,
      },
    });

    console.log(`Created team member ${id} with image: ${imageSrc}`);

    // Return response with appropriate warnings
    if (imageSrc === '/images/default-profile.jpg') {
      return NextResponse.json(
        {
          ...teamMember,
          warning: 'Image upload failed, using default image',
          warnings: uploadWarnings
        },
        {
          status: 201,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }

    // Return with warnings if there were any during successful upload
    if (uploadWarnings && uploadWarnings.length > 0) {
      return NextResponse.json(
        {
          ...teamMember,
          warnings: uploadWarnings
        },
        {
          status: 201,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }

    return NextResponse.json(teamMember, {
      status: 201,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error('Error creating team member:', error);

    // Provide more detailed error information
    let errorMessage = 'Unknown error occurred';
    let statusCode = 500;

    if (error instanceof Error) {
      errorMessage = `Error: ${error.message}`;

      // Check for specific error types
      if (error.message.includes('database') || error.message.includes('prisma')) {
        errorMessage = 'Database error occurred. Please try again later.';
      } else if (error.message.includes('timeout') || error.message.includes('network')) {
        errorMessage = 'Network timeout occurred. Please try again with a smaller image.';
        statusCode = 408; // Request Timeout
      } else if (error.message.includes('storage') || error.message.includes('S3')) {
        errorMessage = 'Storage error occurred. Please try again later.';
      }
    }

    // Set proper content type header to ensure JSON response
    return new NextResponse(JSON.stringify({ error: errorMessage }), {
      status: statusCode,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
}
